<template>
  <div class="property-home">
    <div class="dashboard-header">
      <h1>社区治理端</h1>
      <p class="subtitle">综合管理平台 - 数据总览与功能导航</p>
    </div>
    
    <div class="dashboard-content">
      <!-- 数据概览卡片 -->
      <section class="overview-section">
        <h2 class="section-title">数据概览</h2>
        <div v-if="isLoading" class="loading-container">
          <div class="loading-spinner"></div>
          <p>正在加载数据概览...</p>
        </div>
        <div v-else-if="error" class="error-container">
          <p class="error-message">{{ error }}</p>
          <button @click="loadStatistics" class="retry-btn">重试</button>
        </div>
        <div v-else class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">🆔</div>
            <div class="stat-info">
              <h3>实名认证待审批</h3>
              <p class="stat-number">{{ statistics.pendingVerifications }}</p>
              <span class="stat-change" :class="statistics.pendingVerifications > 0 ? 'attention' : 'neutral'">
                {{ statistics.pendingVerifications > 0 ? '需要处理' : '暂无待审' }}
              </span>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">🏠</div>
            <div class="stat-info">
              <h3>房屋绑定待审批</h3>
              <p class="stat-number">{{ statistics.pendingHouseBindings }}</p>
              <span class="stat-change" :class="statistics.pendingHouseBindings > 0 ? 'attention' : 'neutral'">
                {{ statistics.pendingHouseBindings > 0 ? '需要处理' : '暂无待审' }}
              </span>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">🚗</div>
            <div class="stat-info">
              <h3>车辆绑定待审批</h3>
              <p class="stat-number">{{ statistics.pendingVehicleBindings }}</p>
              <span class="stat-change" :class="statistics.pendingVehicleBindings > 0 ? 'attention' : 'neutral'">
                {{ statistics.pendingVehicleBindings > 0 ? '需要处理' : '暂无待审' }}
              </span>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">📋</div>
            <div class="stat-info">
              <h3>事件管理待分配</h3>
              <p class="stat-number">{{ statistics.pendingIncidents }}</p>
              <span class="stat-change" :class="statistics.pendingIncidents > 0 ? 'attention' : 'neutral'">
                {{ statistics.pendingIncidents > 0 ? '需要分配' : '暂无待分配' }}
              </span>
            </div>
          </div>
        </div>
      </section>

      <!-- 社区治理端功能模块 -->
      <section class="function-section">
        <h2 class="section-title">社区治理功能</h2>
        <div class="grid-container">
          <div class="grid-item" @click="navigateTo('/property/data-overview')">
            <div class="item-icon">📊</div>
            <h3>数据总览</h3>
            <p>总体情况展示, GIS地图展示</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/property/full-archive')">
            <div class="item-icon">📁</div>
            <h3>全息档案</h3>
            <p>档案信息管理与分类, 档案查询与展示, 重点监控人群, 档案导出</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/property/event-management')">
            <div class="item-icon">📋</div>
            <h3>事件管理</h3>
            <p>多源事件整合, 事件查询与状态跟踪, 超时预警</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/property/patrol-management')">
            <div class="item-icon">🚶</div>
            <h3>巡查管理</h3>
            <p>巡查任务编排, 巡查任务发布, 巡查任务执行情况</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/property/gis-grid')">
            <div class="item-icon">🗺️</div>
            <h3>GIS网格管理</h3>
            <p>网格边界编辑, 网格信息维护与绑定, 网格合并与拆分</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/property/info-collection')">
            <div class="item-icon">📝</div>
            <h3>实名信息采集</h3>
            <p>居民实名认证信息采集, 身份验证与管理</p>
          </div>

          <div class="grid-item" @click="navigateTo('/property/vote-management')">
            <div class="item-icon">🗳️</div>
            <h3>投票管理</h3>
            <p>创建投票活动, 设置投票范围, 管理投票结果</p>
          </div>

          <div class="grid-item" @click="navigateTo('/property/house-info-collection')">
            <div class="item-icon">🏠</div>
            <h3>房屋信息采集</h3>
            <p>房屋基础信息采集, 房屋档案建立与维护</p>
          </div>
        </div>
      </section>

      <!-- 公共模块 -->
      <section class="function-section">
        <h2 class="section-title">公共模块</h2>
        <div class="grid-container">
          <!-- 已删除四个无用模块：用户与权限服务、文件存储服务、消息通知服务、日志与监控 -->

          <div class="grid-item" @click="navigateTo('/common/voice-demo')">
            <div class="item-icon">🎤</div>
            <h3>语音文字互转</h3>
            <p>文字转语音, 语音转文字, TTS演示</p>
          </div>
        </div>
      </section>

      <!-- 快速操作区域 -->
      <section class="quick-actions">
        <h2 class="section-title">快速操作</h2>
        <div class="action-buttons">
          <button class="action-btn primary" @click="navigateTo('/property/event-management')">
            <span class="btn-icon">📋</span>
            新建事件
          </button>
          <button class="action-btn secondary" @click="navigateTo('/property/patrol-management')">
            <span class="btn-icon">🚶</span>
            发布巡查任务
          </button>
          <button class="action-btn tertiary" @click="navigateTo('/property/full-archive')">
            <span class="btn-icon">📁</span>
            查看档案
          </button>
        </div>
      </section>
    </div>

    <!-- 语音助手 -->
    <VoiceAssistant ref="voiceAssistant" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import VoiceAssistant from '../components/VoiceAssistant.vue';
import { getToken } from '../utils/tokenManager.js';
import http from '../utils/httpInterceptor.js';

const router = useRouter();

// 数据加载状态
const isLoading = ref(false);
const error = ref('');

// 统计数据
const statistics = reactive({
  pendingVerifications: 0,
  pendingHouseBindings: 0,
  pendingVehicleBindings: 0,
  pendingIncidents: 0
});

// 页面加载时获取统计数据
onMounted(() => {
  loadStatistics();
});

const navigateTo = (path) => {
  router.push(path);
};

// 加载统计数据
const loadStatistics = async () => {
  isLoading.value = true;
  error.value = '';

  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    // 并行获取各种统计数据
    await Promise.all([
      fetchPendingVerifications(),
      fetchPendingHouseBindings(),
      fetchPendingVehicleBindings(),
      fetchPendingIncidents()
    ]);

    console.log('✅ 统计数据加载完成:', statistics);
  } catch (err) {
    console.error('❌ 统计数据加载失败:', err);
    error.value = err.message || '加载统计数据失败';
  } finally {
    isLoading.value = false;
  }
};

// 获取实名认证待审批数量
const fetchPendingVerifications = async () => {
  try {
    const response = await http.get('/auth/list?status=1');
    if (response && response.code === 200) {
      // 从返回体中计算待审批的数量
      statistics.pendingVerifications = response.data?.length || 0;
      console.log('✅ 实名认证待审批数量:', statistics.pendingVerifications);
    }
  } catch (err) {
    console.error('❌ 获取实名认证待审批数量失败:', err);
    statistics.pendingVerifications = 0;
  }
};

// 获取房屋绑定待审批数量
const fetchPendingHouseBindings = async () => {
  try {
    const response = await http.get('/grids/houses/binding/list?status=1&pageNum=1&pageSize=1000');
    if (response && response.code === 200) {
      statistics.pendingHouseBindings = response.data?.length || 0;
      console.log('✅ 房屋绑定待审批数量:', statistics.pendingHouseBindings);
    }
  } catch (err) {
    console.error('❌ 获取房屋绑定待审批数量失败:', err);
    statistics.pendingHouseBindings = 0;
  }
};

// 获取车辆绑定待审批数量
const fetchPendingVehicleBindings = async () => {
  try {
    const response = await http.get('/car/binding/list/1');
    if (response && response.code === 200) {
      // 从返回体中计算待审批的数量
      statistics.pendingVehicleBindings = response.data?.length || 0;
      console.log('✅ 车辆绑定待审批数量:', statistics.pendingVehicleBindings);
    }
  } catch (err) {
    console.error('❌ 获取车辆绑定待审批数量失败:', err);
    statistics.pendingVehicleBindings = 0;
  }
};

// 获取事件管理待分配数量
const fetchPendingIncidents = async () => {
  try {
    const response = await http.get('/incident/list/1'); // 状态1为待分派
    if (response && response.code === 200) {
      statistics.pendingIncidents = response.data?.length || 0;
      console.log('✅ 事件管理待分配数量:', statistics.pendingIncidents);
    }
  } catch (err) {
    console.error('❌ 获取事件管理待分配数量失败:', err);
    statistics.pendingIncidents = 0;
  }
};
</script>

<style scoped>
.property-home {
  max-width: 1200px;
  margin: 0 auto;
  background: #fff;
  min-height: 100vh;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
  background: linear-gradient(135deg, rgb(34, 170, 255), white);
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(19, 6, 210, 0.08);
}

.dashboard-header h1 {
  font-size: 32px;
  color: blue;
  margin: 0 0 10px 0;
  font-weight: 700;
}

.subtitle {
  font-size: 16px;
  color: #333;
  margin: 0;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 40px;
  background: #fff;
}

.section-title {
  font-size: 24px;
  color: rgb(34, 170, 255);
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid rgb(34, 170, 255);
  font-weight: 600;
}

/* 数据概览样式 */
.overview-section {
  background: #fff;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 15px #fff;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 25px;
  background: linear-gradient(135deg, #fff, #d8e8f3);
  border-radius: 10px;
  border: 1px solid #e3f2fd;
  transition: transform 0.3s, box-shadow 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 11, 0.228);
}

.stat-icon {
  font-size: 36px;
  margin-right: 20px;
}

.stat-info h3 {
  font-size: 14px;
  color: #333;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: blue;
  margin: 0 0 5px 0;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 12px;
}

.stat-change.positive {
  color: var(--accent-green);
  background: var(--accent-green-lighter);
}

.stat-change.negative {
  color: var(--error);
  background: rgba(244, 67, 54, 0.1);
}

.stat-change.neutral {
  color: rgb(34, 170, 255);
  background: #e3f2fd;
}

.stat-change.attention {
  color: var(--error);
  background: rgba(244, 67, 54, 0.1);
}

/* 加载和错误状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid rgb(34, 170, 255);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p {
  font-size: 18px;
  color: #888;
  margin: 0;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.error-message {
  font-size: 18px;
  color: var(--error);
  margin: 0 0 20px 0;
}

.retry-btn {
  padding: 12px 24px;
  background: rgb(34, 170, 255);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.3s ease;
}

.retry-btn:hover {
  background: #0284f6;
}

/* 功能模块样式 */
.function-section {
  background: #fff;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 15px #fff;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.grid-item {
  border: 1px solid rgba(248, 248, 248, 0.502);
  padding: 15px;
  border-radius: 12px;
  background: rgb(249, 249, 250);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s, background 0.2s;
  box-shadow: 0 2px 8px rgba(10, 10, 10, 0.315);
}

.grid-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 16px rgba(7, 7, 7, 0.094);
  background: white;
}

.grid-item h3 {
  margin-top: 0;
  color: #222;
}

.grid-item p {
  font-size: 0.9em;
  color: #333;
  line-height: 1.4;
}

/* 快速操作样式 */
.quick-actions {
  background: #fff;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 15px #fff;
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: rgb(34, 170, 255);
  color: white;
}

.action-btn.primary:hover {
  background: #0284f6;
  transform: translateY(-2px);
}

.action-btn.secondary {
  background: #0284f6;
  color: white;
}

.action-btn.secondary:hover {
  background: #028df7;
  transform: translateY(-2px);
}

.action-btn.tertiary {
  background: rgb(34, 170, 255);
  color: rgb(34, 170, 255);
  border: 1px solid rgb(34, 170, 255);
}

.action-btn.tertiary:hover {
  background: rgb(34, 170, 255);
  color: white;
  transform: translateY(-2px);
}

.btn-icon {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-header h1 {
    font-size: 24px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .grid-container {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-btn {
    justify-content: center;
  }
}

.grid-item .yellow-btn {
  background: rgb(34, 170, 255);
  color: #222;
  border: none;
  border-radius: 8px;
  padding: 8px 18px;
  font-weight: 600;
  margin-top: 10px;
  cursor: pointer;
  transition: background 0.2s;
}

.grid-item .yellow-btn:hover {
  background: rgb(34, 170, 255);
  color: #111;
}

/* 黄色按钮通用样式 */
.yellow-btn, .btn, button, .action-btn, .primary, .secondary, .tertiary {
  background: white !important;
  color: #222 !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 10px 22px !important;
  font-weight: 600 !important;
  margin-top: 10px;
  cursor: pointer;
  transition: background 0.2s;
}

.yellow-btn:hover, .btn:hover, button:hover, .action-btn:hover, .primary:hover, .secondary:hover, .tertiary:hover {
  background: rgb(34, 170, 255) !important;
  color: #111 !important;
}
</style>
