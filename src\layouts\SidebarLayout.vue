<template>
  <div class="sidebar-layout">
    <!-- 顶部标题栏 -->
    <header class="header">
      <div class="header-content">
        <h1 class="system-title">芙蓉社区管理系统</h1>
        <div class="header-info">
          <span class="current-time">{{ currentTime }}</span>
          <button @click="goToProfile" class="profile-btn" title="个人信息">
            <span class="profile-icon">👤</span>
            <span class="profile-text">个人信息</span>
          </button>
          <div class="user-info">
            <span class="user-name">{{ user?.username || '用户' }}</span>
            <button @click="debugUserState" class="logout-btn" style="margin-right: 10px;">调试</button>
            <button @click="logout" class="logout-btn">退出</button>
          </div>
        </div>
      </div>
    </header>

    <div class="main-container">
      <!-- 左侧菜单栏 -->
      <aside class="sidebar">
        <nav class="sidebar-nav">
          <!-- 主页链接 -->
          <div class="nav-section">
            <router-link 
              :to="homeRoute" 
              class="nav-item home-item"
              :class="{ active: isHomeActive }"
            >
              <span class="nav-icon">🏠</span>
              <span class="nav-text">{{ homeTitle }}</span>
            </router-link>
          </div>

          <!-- 功能模块 -->
          <div class="nav-section">
            <h3 class="section-title">{{ sectionTitle }}</h3>
            <div class="nav-items">
              <template v-for="item in menuItems" :key="item.path || item.name">
                <!-- 普通菜单项 -->
                <router-link
                  v-if="!item.isDropdown"
                  :to="item.path"
                  class="nav-item"
                  :class="{ active: $route.path === item.path }"
                >
                  <span class="nav-icon">{{ item.icon }}</span>
                  <span class="nav-text">{{ item.name }}</span>
                </router-link>

                <!-- 下拉菜单项 -->
                <div v-else class="nav-dropdown">
                  <div
                    class="nav-item dropdown-toggle"
                    :class="{
                      active: isDropdownActive(item),
                      expanded: expandedDropdowns.includes(item.name)
                    }"
                    @click="toggleDropdown(item.name)"
                  >
                    <span class="nav-icon">{{ item.icon }}</span>
                    <span class="nav-text">{{ item.name }}</span>
                    <span class="dropdown-arrow">
                      {{ expandedDropdowns.includes(item.name) ? '▼' : '▶' }}
                    </span>
                  </div>
                  <div
                    class="dropdown-content"
                    :class="{ expanded: expandedDropdowns.includes(item.name) }"
                  >
                    <router-link
                      v-for="child in item.children"
                      :key="child.path"
                      :to="child.path"
                      class="nav-item nav-sub-item"
                      :class="{ active: $route.path === child.path }"
                    >
                      <span class="nav-icon">{{ child.icon }}</span>
                      <span class="nav-text">{{ child.name }}</span>
                    </router-link>
                  </div>
                </div>
              </template>
            </div>
          </div>

          <!-- 公共模块 - 已隐藏，因为没有可用的模块 -->
          <div v-if="commonItems.length > 0" class="nav-section">
            <h3 class="section-title">公共模块</h3>
            <div class="nav-items">
              <router-link
                v-for="item in commonItems"
                :key="item.path"
                :to="item.path"
                class="nav-item"
                :class="{ active: $route.path === item.path }"
              >
                <span class="nav-icon">{{ item.icon }}</span>
                <span class="nav-text">{{ item.name }}</span>
              </router-link>
            </div>
          </div>
        </nav>
      </aside>

      <!-- 主内容区域 -->
      <main class="content">
        <router-view />
      </main>
    </div>

    <!-- 版权栏 -->
    <footer class="copyright-footer">
      <div class="copyright-content">
        <div class="company-info">
          <GongdaLogo :size="32" class="company-logo" />
          <span class="company-name">软工tech</span>
          <span class="copyright-text">版权所有</span>
        </div>
        <div class="legal-notice">
          <span class="warning-text">侵犯必究</span>
          <span class="year">© {{ currentYear }}</span>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useUserStore } from '../stores/userStore';
import GongdaLogo from '../components/GongdaLogo.vue';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();
// 简化退出登录，不依赖复杂的store操作

const currentTime = ref('');
const expandedDropdowns = ref([]);

// 计算属性
const user = computed(() => userStore.user);
const isProperty = computed(() => userStore.isProperty);
const isResident = computed(() => userStore.isResident);
const currentYear = computed(() => new Date().getFullYear());

// 根据用户类型确定主页路由和标题
const homeRoute = computed(() => {
  return isProperty.value ? '/property-home' : '/resident-home';
});

const homeTitle = computed(() => {
  return isProperty.value ? '首页 (数据总览)' : '首页 (居民服务)';
});

const sectionTitle = computed(() => {
  return isProperty.value ? '社区治理端' : '居民端';
});

const isHomeActive = computed(() => {
  return route.path === homeRoute.value;
});

// 物业管理菜单项
const propertyMenuItems = [
  { path: '/property/data-overview', name: '数据总览', icon: '📊' },
  { path: '/property/full-archive', name: '全息档案', icon: '📁' },
  { path: '/property/event-management', name: '事件管理', icon: '📋' },
  { path: '/property/patrol-management', name: '巡查管理', icon: '🚶' },
  { path: '/property/gis-grid', name: 'GIS网格管理', icon: '🗺️' },
  { path: '/property/vote-management', name: '投票管理', icon: '🗳️' },
  {
    name: '信息采集',
    icon: '📝',
    isDropdown: true,
    children: [
      { path: '/property/info-collection', name: '实名信息采集', icon: '🆔' },
      { path: '/property/house-info-collection', name: '房屋信息采集', icon: '🏠' },
      { path: '/property/vehicle-info-collection', name: '车辆信息采集', icon: '🚗' }
    ]
  }
];

// 居民端菜单项
const residentMenuItems = [
  { path: '/resident/real-name-auth', name: '实名认证', icon: '🆔' },
  { path: '/resident/house-management', name: '房屋管理', icon: '🏠' },
  { path: '/resident/vehicle-management', name: '车辆管理', icon: '🚗' },
  { path: '/resident/family-management', name: '家人管理', icon: '👨‍👩‍👧‍👦' },
  { path: '/resident/issue-report', name: '问题上报', icon: '📢' },
  { path: '/resident/budget-vote', name: '预算支出投票', icon: '🗳️' }
];

// 公共模块菜单项 - 已删除无用模块
const commonItems = [
  // 删除了：用户与权限服务、文件存储服务、消息通知服务、日志与监控
];

// 根据用户类型选择菜单项
const menuItems = computed(() => {
  console.log('🔍 menuItems computed - 用户状态详情:', {
    userType: userStore.userType,
    isResident: userStore.isResident,
    isProperty: userStore.isProperty,
    'isProperty.value': isProperty.value,
    'isResident.value': isResident.value,
    'user': userStore.user,
    'localStorage.userType': localStorage.getItem('userType'),
    'localStorage.userData': localStorage.getItem('userData')
  });

  const selectedMenu = isProperty.value ? propertyMenuItems : residentMenuItems;
  console.log('🔍 选择的菜单类型:', isProperty.value ? 'property (治理端)' : 'resident (居民端)');
  console.log('🔍 菜单项:', selectedMenu);

  return selectedMenu;
});

// 更新时间
const updateTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  
  currentTime.value = `${year}年${month}月${day}日  ${hours}:${minutes}:${seconds}`;
};

// 跳转到个人信息页面
const goToProfile = () => {
  // 根据用户类型跳转到对应的个人信息页面
  if (isResident.value) {
    router.push('/resident-home/profile');
  } else if (isProperty.value) {
    router.push('/property-home/profile');
  }
};

// 下拉菜单相关方法
const toggleDropdown = (dropdownName) => {
  const index = expandedDropdowns.value.indexOf(dropdownName);
  if (index > -1) {
    expandedDropdowns.value.splice(index, 1);
  } else {
    expandedDropdowns.value.push(dropdownName);
  }
};

const isDropdownActive = (item) => {
  if (!item.children) return false;
  return item.children.some(child => route.path === child.path);
};

// 调试用户状态
const debugUserState = () => {
  const debugInfo = {
    'userStore.userType': userStore.userType,
    'userStore.isResident': userStore.isResident,
    'userStore.isProperty': userStore.isProperty,
    'userStore.user': userStore.user,
    'localStorage.userType': localStorage.getItem('userType'),
    'localStorage.userData': localStorage.getItem('userData'),
    'localStorage.userToken': localStorage.getItem('userToken'),
    'isProperty.value': isProperty.value,
    'isResident.value': isResident.value,
    'menuItems': menuItems.value,
    'homeTitle': homeTitle.value,
    'sectionTitle': sectionTitle.value
  };

  console.log('🔍 调试用户状态:', debugInfo);
  dialog.showInfo('调试信息已输出到控制台，请查看Console标签页', '调试信息');
};

// 退出登录 - 最简化版本，直接清理数据
const logout = () => {
  console.log('🚪 退出登录按钮被点击');

  // 使用原生confirm确保功能稳定
  const confirmed = confirm('确定要退出登录吗？');
  console.log('用户确认结果:', confirmed);

  if (confirmed) {
    try {
      console.log('✅ 开始执行退出登录...');

      // 直接清理localStorage数据
      localStorage.removeItem('userToken');
      localStorage.removeItem('userType');
      localStorage.removeItem('userData');
      localStorage.removeItem('auth_data');
      console.log('✅ localStorage数据已清理');

      // 清理sessionStorage数据
      sessionStorage.removeItem('auth_data');
      console.log('✅ sessionStorage数据已清理');

      // 显示成功消息
      console.log('✅ 退出登录成功！');

      // 直接跳转到登录页
      window.location.href = '/login';
      console.log('✅ 正在跳转到登录页...');

    } catch (error) {
      console.error('❌ 退出登录失败:', error);
      alert('退出登录失败，请重试: ' + error.message);
    }
  } else {
    console.log('❌ 用户取消退出登录');
  }
};

let timeInterval;

onMounted(() => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);

  // 确保用户状态已初始化
  userStore.initializeUser();
  console.log('🔍 SidebarLayout mounted - 用户状态:', {
    userType: userStore.userType,
    isResident: userStore.isResident,
    isProperty: userStore.isProperty,
    isAuthenticated: userStore.isAuthenticated
  });

  // 延迟检查，确保响应式更新完成
  setTimeout(() => {
    console.log('🔍 SidebarLayout mounted (延迟检查) - 用户状态:', {
      userType: userStore.userType,
      isResident: userStore.isResident,
      isProperty: userStore.isProperty,
      isAuthenticated: userStore.isAuthenticated,
      'localStorage.userType': localStorage.getItem('userType'),
      'localStorage.userData': localStorage.getItem('userData')
    });
  }, 100);
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});
</script>

<style scoped>
.sidebar-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: blue;
}

.header {
  background: linear-gradient(90deg, #0037ff 100%, #e3f2fd 100%);
  color: #fff ;
  padding: 0;

  z-index: 1000;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 30px;
  max-width: 100%;
}

.system-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
  color: #fff;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.current-time {
  font-size: 16px;
  font-weight: 500;
  color: #fff;
}

.profile-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: rgb(244, 244, 245);
  border: 1px solid rgb(241, 241, 244);
  border-radius: 8px;
  color: #fff;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.profile-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  box-shadow: none;
  color: #fff;
}

.profile-icon {
  font-size: 16px;
}

.profile-text {
  font-weight: 500;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-name {
  font-size: 16px;
  font-weight: 500;
  color: #fff;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.logout-btn:hover {
  background: rgba(245, 243, 243, 0.3);
  color: #fff;
}

.main-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sidebar {
  width: 250px;
  background: #f20505 ;
  color: #222 !important;
  overflow-y: auto;

  border-right: 1px solid #e3f2fd !important;
}

.sidebar-nav {
  padding: 20px 0;
}

.nav-section {
  margin-bottom: 30px;
}

.section-title {
  color: #1976d2 !important;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin: 0 20px 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #1976d2 !important;
  background: linear-gradient(90deg, #1976d2 60%, #e3f2fd 100%) !important;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.nav-items {
  display: flex;
  flex-direction: column;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: rgb(246, 246, 248);
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  margin: 2px 8px;
  border-radius: 8px;
}

.nav-item:hover {
  background-color: blue;
  border-left-color: blue;
  color: blue;
}

.nav-item.active {
  background-color: blue;
  border-left-color: blue;
  color: blue;
}

.nav-item.home-item {
  background: #1976d2 !important;
  color: #fff !important;
  border-left: none !important;
  font-weight: 600 !important;
  box-shadow: none !important;
}

.nav-item.home-item:hover, .nav-item.home-item.active {
  background: #1565c0 !important;
  color: #fff !important;
  box-shadow: 0 4px 16px rgba(25, 118, 210, 0.15) !important;
}

.nav-item:hover, .nav-item.active {
  background: #e3f2fd !important;
  color: #1976d2 !important;
  border-left-color: #1976d2 !important;
  box-shadow: 0 4px 16px rgba(25, 118, 210, 0.15) !important;
}

.nav-icon {
  font-size: 18px;
  margin-right: 12px;
  width: 20px;
  text-align: center;
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
}

/* 下拉菜单样式 */
.nav-dropdown {
  margin-bottom: 4px;
}

.dropdown-toggle {
  position: relative;
  cursor: pointer;
  justify-content: space-between;
}

.dropdown-toggle.expanded {
  background: var(--primary-yellow-light);
  color: blue;
}

.dropdown-arrow {
  font-size: 12px;
  transition: transform 0.3s ease;
  margin-left: auto;
}

.dropdown-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background: var(--primary-yellow-light);
  border-radius: 0 0 8px 8px;
  margin-top: -4px;
}

.dropdown-content.expanded {
  max-height: 300px;
}

.nav-sub-item {
  padding: 12px 20px 12px 50px;
  margin: 0;
  border-radius: 0;
  background: transparent;
  border-left: 3px solid transparent;
}

.nav-sub-item:hover {
  background: var(--primary-yellow-light);
  border-left-color: var(--primary-yellow);
}

.nav-sub-item.active {
  background: var(--primary-yellow);
  color: #222;
  border-left-color: var(--primary-yellow-dark);
  transform: none;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.content {
  flex: 1;
  padding: 30px;
  background-color: #fff;
  overflow-y: auto;
}

/* 版权栏样式 */
.copyright-footer {
  background: #0318f9 !important;
  color: #fff !important;
  padding: 15px 0;
  border-top: 1px solid #e3f2fd !important;
  box-shadow: none !important;
}

.copyright-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 30px;
}

.company-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.company-logo {
  filter: none !important;
}

.company-name {
  font-size: 18px;
  font-weight: 700;
  color: #fff !important;
  text-shadow: none !important;
}

.copyright-text {
  font-size: 14px;
  font-weight: 500;
  opacity: 0.9;
  color: #fff !important;
}

.legal-notice {
  display: flex;
  align-items: center;
  gap: 20px;
}

.warning-text {
  font-size: 14px;
  font-weight: 600;
  color: #fff !important;
  text-shadow: none !important;
}

.year {
  font-size: 14px;
  font-weight: 500;
  opacity: 0.8;
  color: #fff !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 200px;
  }

  .header-content {
    padding: 10px 20px;
  }

  .header-info {
    gap: 15px;
  }

  .profile-btn {
    padding: 8px 12px;
    font-size: 12px;
  }

  .profile-text {
    display: none;
  }

  .profile-icon {
    font-size: 18px;
  }

  .system-title {
    font-size: 20px;
  }

  .content {
    padding: 20px;
  }

  .copyright-content {
    flex-direction: column;
    gap: 10px;
    text-align: center;
    padding: 0 20px;
  }

  .company-info,
  .legal-notice {
    gap: 10px;
  }

  .company-name,
  .warning-text {
    font-size: 16px;
  }

  .copyright-text,
  .year {
    font-size: 12px;
  }
}

.header, .sidebar, .copyright-footer {
  background: #4fa3f7 !important;
  color: #222 !important;
  box-shadow: none !important;
}

.header, .header-content, .system-title, .header-info, .current-time, .profile-btn, .profile-btn:hover, .profile-icon, .profile-text, .user-info, .user-name, .logout-btn, .logout-btn:hover {
  box-shadow: none !important;
  text-shadow: none !important;
  filter: none !important;
}

.sidebar {
  background: #fff !important;
  color: #222 !important;
  border-right: 1px solid #e3f2fd !important;
  box-shadow: none !important;
}

.sidebar-nav, .nav-section, .section-title, .nav-items, .nav-item, .nav-item.home-item, .nav-item:hover, .nav-item.active, .nav-sub-item, .nav-sub-item:hover, .nav-sub-item.active, .nav-dropdown, .dropdown-toggle, .dropdown-content, .dropdown-content.expanded {
  background: transparent !important;
  color: #222 !important;
  border: none !important;
  box-shadow: none !important;
}

.section-title {
  color: #1976d2 !important;
  border-bottom: 1px solid #1976d2 !important;
  background: linear-gradient(90deg, #1976d2 60%, #e3f2fd 100%) !important;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.nav-item, .nav-item.home-item {
  background: transparent !important;
  color: #222 !important;
  border-left: 3px solid transparent !important;
}

.nav-item.home-item {
  background: #1976d2 !important;
  color: #fff !important;
  border-left: none !important;
  font-weight: 600 !important;
}

.nav-item.home-item:hover, .nav-item.home-item.active {
  background: #1565c0 !important;
  color: #fff !important;
  box-shadow: 0 4px 16px rgba(25, 118, 210, 0.15) !important;
}

.nav-item:hover, .nav-item.active {
  background: #e3f2fd !important;
  color: #1976d2 !important;
  border-left-color: #1976d2 !important;
  box-shadow: 0 4px 16px rgba(25, 118, 210, 0.15) !important;
}

.copyright-footer {
  background: #1976d2 !important;
  color: #fff !important;
  border-top: 1px solid #e3f2fd !important;
  box-shadow: none !important;
}

.company-logo, .company-name, .copyright-text, .legal-notice, .warning-text, .year {
  filter: none !important;
  text-shadow: none !important;
  color: #fff !important;
}

.profile-section, .function-section, .quick-actions, .announcements {
  background: #e3f2fd !important;
  box-shadow: none !important;
  color: #222 !important;
}

.section-title, .card-title {
  border-bottom: 2px solid #1976d2 !important;
  color: #1976d2 !important;
}

.btn, .action-btn, button {
  background: #1976d2 !important;
  color: #fff !important;
  border-radius: 8px !important;
  border: none !important;
  font-weight: 600 !important;
  box-shadow: none !important;
}
.btn:hover, .action-btn:hover, button:hover {
  background: #e3f2fd !important;
  color: #1976d2 !important;
  box-shadow: 0 4px 16px rgba(25, 118, 210, 0.15) !important;
}

body, .header, .sidebar, .copyright-footer, .content, .main-container, .profile-section, .function-section, .quick-actions, .announcements {
  color: var(--text-dark);
}
</style>

